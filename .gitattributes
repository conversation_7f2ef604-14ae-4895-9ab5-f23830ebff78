# Set the default behavior, in case people don't have core.autocrlf set.
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.py text
*.md text
*.txt text
*.json text
*.toml text
*.yaml text
*.yml text

# Declare files that will always have CRLF line endings on checkout.
*.bat text eol=crlf
*.cmd text eol=crlf

# Declare files that will always have LF line endings on checkout.
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.pkl binary
*.parquet binary
*.zip binary
*.tar.gz binary
