#!/usr/bin/env python3
"""
模型训练和回测系统
基于03优化参数的模型训练、回测、评估功能
"""

import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime
import json
from typing import Dict, List, Tuple, Optional
import joblib
import random

# 机器学习相关
import xgboost as xgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score
from dataclasses import dataclass

warnings.filterwarnings('ignore')

# 设置全局随机种子确保结果可重复
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    shares: int
    buy_price: float
    buy_date: datetime
    current_price: float = 0.0
    
    @property
    def market_value(self) -> float:
        return self.shares * self.current_price
    
    @property
    def unrealized_return(self) -> float:
        return (self.current_price - self.buy_price) / self.buy_price

class ModelTrainer:
    """模型训练器 - 集成版"""
    
    def __init__(self):
        self.model = None
        self.feature_importance = None
        
        # 尝试加载03的优化参数
        self.xgb_params = self._load_optimized_params()
    
    def _load_optimized_params(self) -> Dict:
        """加载03步骤的优化参数，如果不存在则使用默认参数"""
        # 默认参数
        default_params = {
            'objective': 'binary:logistic',
            'use_label_encoder': False,
            'base_score': 0.5,
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42
        }

        # 尝试加载03步骤的优化参数
        optimized_params_file = Path("model/optimized_params/optimized_params.json")
        if optimized_params_file.exists():
            try:
                with open(optimized_params_file, 'r', encoding='utf-8') as f:
                    optimized_data = json.load(f)

                # 获取Walk-Forward分析的最佳参数
                walk_forward_analysis = optimized_data.get('walk_forward_analysis', {})
                best_params = walk_forward_analysis.get('best_params', {})
                best_auc = walk_forward_analysis.get('best_window_auc', 0)

                if best_params:
                    # 验证参数是否合理，避免过度交易
                    if self._validate_params(best_params, best_auc):
                        # 合并优化参数和默认参数
                        params = {**default_params, **best_params}

                        print(f"✅ 已加载验证过的03步骤优化参数 (最佳AUC: {best_auc:.4f})")
                        print(f"  参数: n_estimators={params['n_estimators']}, max_depth={params['max_depth']}, lr={params['learning_rate']:.4f}")
                        print(f"  来源: Walk-Forward分析窗口{walk_forward_analysis.get('best_window_id', 'N/A')}")

                        return params
                    else:
                        print(f"⚠️ 03优化参数未通过验证，使用稳健默认参数")
                        print(f"  拒绝原因: max_depth={best_params.get('max_depth', 'N/A')}, lr={best_params.get('learning_rate', 'N/A'):.4f}")
                        print(f"  这些参数可能导致过度交易 (376笔交易, -14.15%收益)")
                        return default_params
                else:
                    print("⚠️ 03步骤参数文件中未找到best_params")

            except Exception as e:
                print(f"⚠️ 加载03步骤参数失败: {e}")
        else:
            print("⚠️ 未找到03步骤优化参数文件")

        print("使用默认参数")
        return default_params

    def _validate_params(self, params: Dict, auc: float) -> bool:
        """验证参数是否合理，避免过度拟合"""
        if not params:
            return False

        # 检查关键参数是否在合理范围内
        max_depth = params.get('max_depth', 6)
        learning_rate = params.get('learning_rate', 0.1)
        n_estimators = params.get('n_estimators', 100)

        # 过度拟合的典型特征
        if max_depth < 5:  # 太浅可能欠拟合
            return False
        if max_depth > 8:  # 太深可能过拟合
            return False
        if learning_rate < 0.08:  # 学习率太小，可能过拟合
            return False
        if learning_rate > 0.3:  # 学习率太大
            return False
        if n_estimators > 200:  # 树太多可能过拟合
            return False
        if auc > 0.75:  # AUC过高可能过拟合
            return False

        return True

    def _load_walk_forward_results(self) -> Dict:
        """加载最新的Walk-Forward分析结果"""
        results_dir = Path("logs/walk_forward_results")
        if not results_dir.exists():
            return {}
        
        # 找到最新的结果文件
        result_files = list(results_dir.glob("walk_forward_analysis_*.json"))
        if not result_files:
            return {}
        
        latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载Walk-Forward结果失败: {e}")
            return {}
    
    def train_model(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """训练XGBoost模型"""
        print("开始训练XGBoost模型...")
        
        # 数据分割
        X_train_split, X_val, y_train_split, y_val = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
        )
        
        # 创建和训练模型
        self.model = xgb.XGBClassifier(**self.xgb_params)
        
        # 确保数据格式正确
        X_train_values = X_train_split.values if hasattr(X_train_split, 'values') else X_train_split
        y_train_values = y_train_split.values if hasattr(y_train_split, 'values') else y_train_split
        X_val_values = X_val.values if hasattr(X_val, 'values') else X_val
        
        self.model.fit(X_train_values, y_train_values)
        
        # 计算特征重要性
        feature_names = X_train.columns if hasattr(X_train, 'columns') else [f'feature_{i}' for i in range(X_train_values.shape[1])]
        self.feature_importance = pd.DataFrame({
            'feature': feature_names,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        # 评估模型
        y_pred_train = self.model.predict_proba(X_train_values)[:, 1]
        y_pred_val = self.model.predict_proba(X_val_values)[:, 1]
        
        train_auc = roc_auc_score(y_train_values, y_pred_train)
        val_auc = roc_auc_score(y_val, y_pred_val)
        
        results = {
            'train_auc': train_auc,
            'val_auc': val_auc,
            'train_accuracy': (self.model.predict(X_train_values) == y_train_values).mean(),
            'val_accuracy': (self.model.predict(X_val_values) == y_val).mean()
        }
        
        print(f"训练完成:")
        print(f"  训练集AUC: {train_auc:.4f}")
        print(f"  验证集AUC: {val_auc:.4f}")
        print(f"  训练集准确率: {results['train_accuracy']:.4f}")
        print(f"  验证集准确率: {results['val_accuracy']:.4f}")
        
        return results
    
    def save_model(self, filename: str = "xgboost_model.pkl") -> str:
        """保存模型"""
        models_dir = Path("model/saved_models")
        models_dir.mkdir(exist_ok=True)
        
        model_path = models_dir / filename
        joblib.dump(self.model, model_path)
        print(f"模型已保存到: {model_path}")
        return str(model_path)
    
    def get_feature_importance(self, top_n: int = 10) -> pd.DataFrame:
        """获取特征重要性"""
        if self.feature_importance is not None:
            return self.feature_importance.head(top_n)
        return pd.DataFrame()

class BacktestEngine:
    """量化交易策略回测引擎"""
    
    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}
        self.trade_records = []
        self.daily_portfolio_value = []
        
        # 交易成本参数
        self.commission_rate = 0.001  # 0.1%手续费
        self.slippage_rate = 0.001    # 0.1%滑点
        
        # 策略参数（保守优化版本 - 基于历史成功经验）
        self.probability_threshold = 0.55  # 提高买入门槛，确保交易质量
        self.top_k_stocks = 5  # 减少持仓数量，降低风险
        self.take_profit_threshold = 0.12  # 更高的止盈目标，让利润充分增长
        self.stop_loss_threshold = 0.15  # 更宽松的止损，避免频繁止损
        self.max_holding_days = 15  # 延长持仓时间，让趋势充分发展
        
    def run_backtest(self, model, test_data: pd.DataFrame, feature_columns: List[str]) -> Dict:
        """运行回测"""
        print("开始回测...")
        
        # 按日期分组
        test_dates = sorted(test_data['date'].unique())
        
        for i, current_date in enumerate(test_dates):
            current_data = test_data[test_data['date'] == current_date].set_index('symbol')
            
            # 更新持仓价格
            self._update_positions_price(current_data)
            
            # 执行卖出逻辑
            self._execute_sell_logic(current_data, current_date)
            
            # 执行买入逻辑
            if i < len(test_dates) - self.max_holding_days:  # 确保有足够时间持有
                self._execute_buy_logic(model, current_data, feature_columns, current_date)
            
            # 记录组合价值
            portfolio_value = self.cash + sum(pos.market_value for pos in self.positions.values())
            self.daily_portfolio_value.append(portfolio_value)
        
        return self._calculate_results()
    
    def _update_positions_price(self, current_data: pd.DataFrame):
        """更新持仓价格"""
        for symbol, position in self.positions.items():
            if symbol in current_data.index:
                position.current_price = current_data.loc[symbol, 'close']
    
    def _execute_sell_logic(self, current_data: pd.DataFrame, current_date: datetime):
        """执行卖出逻辑"""
        symbols_to_sell = []
        
        for symbol, position in self.positions.items():
            if symbol not in current_data.index:
                continue
                
            current_price = current_data.loc[symbol, 'close']
            days_held = (current_date - position.buy_date).days
            return_rate = (current_price - position.buy_price) / position.buy_price
            
            # 卖出条件
            sell_reason = None
            if return_rate >= self.take_profit_threshold:
                sell_reason = "TAKE_PROFIT"
            elif return_rate <= -self.stop_loss_threshold:
                sell_reason = "STOP_LOSS"
            elif days_held >= self.max_holding_days:
                sell_reason = "TIME_LIMIT"
            
            if sell_reason:
                # 卖出（考虑交易成本）
                gross_sell_value = position.shares * current_price
                # 计算交易成本
                commission = gross_sell_value * self.commission_rate
                slippage = gross_sell_value * self.slippage_rate
                net_sell_value = gross_sell_value - commission - slippage
                
                self.cash += net_sell_value
                
                # 记录交易
                self.trade_records.append({
                    'date': current_date,
                    'symbol': symbol,
                    'action': 'SELL',
                    'shares': position.shares,
                    'price': current_price,
                    'value': net_sell_value,
                    'reason': sell_reason,
                    'return': return_rate
                })
                
                print(f"卖出 {symbol}: {position.shares}股 @ {current_price:.2f}, 原因: {sell_reason}, 收益率: {return_rate:.2%}")
                symbols_to_sell.append(symbol)
        
        # 清理已卖出的持仓
        for symbol in symbols_to_sell:
            del self.positions[symbol]
    
    def _execute_buy_logic(self, model, current_data: pd.DataFrame, feature_columns: List[str], current_date: datetime):
        """执行买入逻辑"""
        # 获取可用特征
        available_features = [col for col in feature_columns if col in current_data.columns]
        if not available_features:
            return
        
        # 预测概率
        X = current_data[available_features]
        X_values = X.values if hasattr(X, 'values') else X
        probabilities = model.predict_proba(X_values)
        
        # 筛选买入候选，增加风险控制
        buy_candidates = []
        total_candidates = len(current_data.index)
        prob_filter_count = 0
        risk_filter_count = 0
        
        for i, symbol in enumerate(current_data.index):
            prob = probabilities[i, 1]  # 正类概率
            if prob > self.probability_threshold and symbol not in self.positions:
                prob_filter_count += 1
                # 计算风险评分（基于波动性和技术指标）
                volatility = current_data.loc[symbol, 'volatility'] if 'volatility' in current_data.columns else 0.02
                rsi = current_data.loc[symbol, 'rsi'] if 'rsi' in current_data.columns else 50
                
                # 更严格的风险控制：避免买入过度超买或高波动的股票
                if rsi < 80 and volatility < 3.0:  # 更严格的波动性和RSI控制
                    risk_filter_count += 1
                    # 计算调整后的分数（概率 - 风险惩罚）
                    risk_adjusted_score = prob - (volatility * 0.1) - (max(0, rsi - 70) * 0.001)
                    buy_candidates.append((symbol, prob, risk_adjusted_score))
        
        # 调试信息（仅在有买入候选时输出）
        if len(buy_candidates) > 0:
            print(f"  筛选统计: 总候选{total_candidates}, 概率过滤后{prob_filter_count}, 风险过滤后{risk_filter_count}, 最终候选{len(buy_candidates)}")
        
        # 按风险调整后分数排序，选择前K个
        buy_candidates.sort(key=lambda x: x[2], reverse=True)
        selected_stocks = buy_candidates[:self.top_k_stocks]
        
        if not selected_stocks:
            return
        
        # 智能资金分配（基于风险调整分数）
        available_cash = self.cash * 0.9  # 保留10%现金
        
        # 计算总风险调整分数
        total_score = sum(score for _, _, score in selected_stocks)
        
        for symbol, prob, score in selected_stocks:
            price = current_data.loc[symbol, 'close']
            
            # 根据风险调整分数分配资金
            if total_score > 0:
                allocation_ratio = score / total_score
                cash_allocation = available_cash * allocation_ratio
            else:
                cash_allocation = available_cash / len(selected_stocks)
                
            shares = int(cash_allocation / price)
            
            if shares > 0:
                # 计算买入成本（含交易成本）
                gross_cost = shares * price
                commission = gross_cost * self.commission_rate
                slippage = gross_cost * self.slippage_rate
                total_cost = gross_cost + commission + slippage
                
                if total_cost <= self.cash:
                    # 买入
                    self.cash -= total_cost
                    
                    # 记录交易
                    self.trade_records.append({
                        'date': current_date,
                        'symbol': symbol,
                        'action': 'BUY',
                        'shares': shares,
                        'price': price,
                        'cost': total_cost,
                        'probability': prob,
                        'score': score
                    })
                    
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        shares=shares,
                        buy_price=price,  # 使用原价作为基准价
                        buy_date=current_date,
                        current_price=price
                    )
                    print(f"买入 {symbol}: {shares}股 @ {price:.2f} (概率:{prob:.3f}, 分数:{score:.3f})")
    
    def _calculate_results(self) -> Dict:
        """计算回测结果"""
        if not self.daily_portfolio_value:
            return {}
        
        final_value = self.daily_portfolio_value[-1]
        total_return = (final_value / self.initial_capital) - 1
        
        # 计算每日收益率
        portfolio_values = np.array(self.daily_portfolio_value)
        daily_returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # 计算风险指标
        volatility = np.std(daily_returns) * np.sqrt(252)
        sharpe_ratio = (total_return * 252 / len(daily_returns)) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown)
        
        results = {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'final_value': final_value,
            'total_trades': len(self.trade_records),
            'daily_portfolio_value': self.daily_portfolio_value
        }
        
        print(f"回测完成:")
        print(f"  总收益率: {total_return:.2%}")
        print(f"  最大回撤: {max_drawdown:.2%}")
        print(f"  夏普比率: {sharpe_ratio:.4f}")
        
        return results

class QuantSystem:
    """基于机器学习的量化交易系统"""
    
    def __init__(self):
        self.trainer = ModelTrainer()
        self.backtest_engine = BacktestEngine()
        
        print("=== 基于机器学习的量化交易系统 ===")
        print("架构: 01数据获取 → 02特征管理 → 03模型优化 → 04模型训练")
        print("模型: XGBoost二分类，预测10日内5%上涨概率")
        print()
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 优先使用02脚本准备的数据
        train_file = Path("model/data/training_data.parquet")
        test_file = Path("model/data/test_data.parquet")
        
        if train_file.exists() and test_file.exists():
            print("正在加载02脚本准备的数据...")
            train_data = pd.read_parquet(train_file)
            test_data = pd.read_parquet(test_file)
        else:
            print("❌ 未找到02脚本准备的数据，请先运行: python 02_use_feature_store.py")
            return pd.DataFrame(), pd.DataFrame()
        
        print(f"训练数据: {len(train_data):,} 条")
        print(f"测试数据: {len(test_data):,} 条")
        
        return train_data, test_data
    
    def calculate_target_variable(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算目标变量：未来10个交易日内是否上涨超过5%"""
        df = data.copy()
        
        # 技术报告要求
        target_window = 10  # 10个交易日
        target_threshold = 0.05  # 5%上涨阈值
        
        # 计算未来回报率
        df['future_return'] = df.groupby('symbol')['close'].transform(
            lambda x: x.pct_change(periods=target_window).shift(-target_window)
        )
        
        # 生成目标标签
        df['target'] = (df['future_return'] > target_threshold).astype(int)
        
        return df
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
        """准备特征数据"""
        # 首先计算目标变量（如果不存在）
        if 'target' not in data.columns:
            print("计算目标变量...")
            data = self.calculate_target_variable(data)

        # 加载最优特征配置
        optimal_features = self._load_optimal_features()

        # 检查可用特征
        available_features = [f for f in optimal_features if f in data.columns]
        missing_features = [f for f in optimal_features if f not in data.columns]

        if missing_features:
            print(f"⚠️ 缺失最优特征: {missing_features}")

        print(f"📊 使用最优特征配置: {len(available_features)}/32 个特征")

        # 技术报告要求的关键特征（映射到实际列名）
        key_features = [
            'price_change',    # 对应mean_return_30d的概念
            'rsi',            # RSI指标
            'macd',           # MACD指标
            'sma_20'          # 移动平均线
        ]
        
        # 数据清理
        clean_data = data.copy()
        
        # 替换无限值
        for col in available_features:
            clean_data[col] = clean_data[col].replace([np.inf, -np.inf], np.nan)
        
        # 移除包含NaN的行
        clean_data = clean_data.dropna(subset=available_features + ['target'])
        
        X = clean_data[available_features]
        y = clean_data['target']
        
        print(f"特征数量: {len(available_features)}")
        print(f"关键特征: {[f for f in key_features if f in available_features]}")
        print(f"样本数量: {len(X):,}")
        print(f"目标分布: {y.value_counts().to_dict()}")
        
        return X, y, available_features

    def _load_optimal_features(self) -> List[str]:
        """加载最优特征配置"""
        optimal_features_file = Path("model/optimal_features.json")

        if optimal_features_file.exists():
            try:
                with open(optimal_features_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return config['selected_features']
            except Exception as e:
                print(f"⚠️ 加载最优特征配置失败: {e}")

        # 回退到硬编码的32个最优特征
        print("使用硬编码的32个最优特征")
        return [
            'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
            'macd', 'macd_signal', 'macd_histogram', 'rsi',
            'sma_20', 'ema_12', 'ema_26',
            'bb_upper', 'bb_middle', 'bb_lower', 'atr',
            'volume_sma', 'price_momentum', 'volatility',
            'price_to_sma55_pct', 'price_to_sma233_pct',
            'proximity_to_sma55', 'proximity_to_sma233',
            'sma55_position', 'sma233_position',
            'sma55_support_strength', 'sma233_support_strength',
            'trading_intensity', 'volume_price_trend',
            'bb_position', 'price_range', 'price_range_pct', 'price_position'
        ]

    def run_full_pipeline(self):
        """运行完整流程"""
        print("开始运行完整的量化交易系统...\n")
        
        try:
            # 1. 加载数据
            train_data, test_data = self.load_data()
            if train_data.empty or test_data.empty:
                return
            
            # 2. 准备训练数据
            print("\n=== 准备训练特征 ===")
            X_train, y_train, feature_columns = self.prepare_features(train_data)
            
            # 3. 训练模型
            print("\n=== 训练模型 ===")
            training_results = self.trainer.train_model(X_train, y_train)
            
            # 保存模型
            model_path = self.trainer.save_model()
            
            # 显示特征重要性
            feature_importance = self.trainer.get_feature_importance(10)
            print("\n前10个最重要特征:")
            for _, row in feature_importance.iterrows():
                print(f"  {row['feature']}: {row['importance']:.4f}")
            
            # 4. 准备测试数据
            print("\n=== 准备测试特征 ===")
            X_test, y_test, _ = self.prepare_features(test_data)
            
            # 5. 运行回测
            print("\n=== 运行回测 ===")
            strategy_results = self.backtest_engine.run_backtest(
                self.trainer.model, test_data, feature_columns
            )
            
            # 6. 保存结果
            self._save_results(training_results, strategy_results, feature_importance)
            
            print("\n✅ 量化交易系统运行完成！")
            
        except Exception as e:
            print(f"\n❌ 系统运行出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _save_results(self, training_results: Dict, strategy_results: Dict, feature_importance: pd.DataFrame):
        """保存结果"""
        print("\n=== 保存结果 ===")
        
        results_dir = Path("logs/model_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"trading_results_{timestamp}.json"
        
        # 准备保存数据
        save_data = {
            'timestamp': timestamp,
            'training_results': training_results,
            'strategy_results': {
                'total_return': strategy_results.get('total_return', 0),
                'max_drawdown': strategy_results.get('max_drawdown', 0),
                'sharpe_ratio': strategy_results.get('sharpe_ratio', 0),
                'final_value': strategy_results.get('final_value', 0),
                'total_trades': strategy_results.get('total_trades', 0)
            },
            'top_features': feature_importance.to_dict('records')
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到: {results_file}")

def main():
    """主函数"""
    import sys

    print("=== 基于机器学习的量化交易策略系统 ===")

    # 创建系统实例
    system = QuantSystem()

    # 解析命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "--auto":
            print("🤖 自动化模式：运行完整流程")
            system.run_full_pipeline()
        elif arg == "--train-only":
            print("🎯 仅训练模型模式")
            train_data, _ = system.load_data()
            if not train_data.empty:
                X_train, y_train, _ = system.prepare_features(train_data)
                system.trainer.train_model(X_train, y_train)
                system.trainer.save_model()
        elif arg == "--backtest-only":
            print("📊 仅回测模式（需要现有模型）")
            print("⚠️ 此功能待实现")
        else:
            print(f"❌ 未知参数: {arg}")
            print("支持的参数: --auto, --train-only, --backtest-only")
            return
    else:
        # 交互模式
        print("\n请选择运行模式:")
        print("1. 完整流程 - 训练模型并回测（推荐）")
        print("2. 仅训练模型 - 保存模型文件")
        print("3. 仅回测分析 - 使用现有模型")
        print("4. 快速验证 - 小数据集测试")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            print("🚀 运行完整流程...")
            system.run_full_pipeline()
        elif choice == "2":
            print("🎯 仅训练模型...")
            train_data, _ = system.load_data()
            if not train_data.empty:
                X_train, y_train, _ = system.prepare_features(train_data)
                system.trainer.train_model(X_train, y_train)
                system.trainer.save_model()
        elif choice == "3":
            print("📊 仅回测分析...")
            print("⚠️ 此功能待实现 - 需要加载现有模型")
        elif choice == "4":
            print("⚡ 快速验证模式...")
            print("💡 建议使用: python 02_use_feature_store.py --auto 进行快速测试")
        else:
            print("无效选择，运行完整流程...")
            system.run_full_pipeline()

if __name__ == "__main__":
    main()