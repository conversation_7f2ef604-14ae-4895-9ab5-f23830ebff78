#!/usr/bin/env python3
import pandas as pd
import os
from pathlib import Path

def diagnose_timestamp_format():
    """诊断时间戳格式问题"""
    data_dir = Path("data/timeframe=1d")
    
    # 找几个文件来检查
    sample_files = []
    for symbol_dir in list(data_dir.glob("symbol=*"))[:3]:  # 只检查前3个
        parquet_files = list(symbol_dir.glob("*.parquet"))
        if parquet_files:
            sample_files.append((symbol_dir.name.replace("symbol=", ""), parquet_files[0]))
    
    for symbol, file_path in sample_files:
        try:
            print(f"\n=== 检查 {symbol} ===")
            df = pd.read_parquet(file_path)
            print(f"形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            if 'timestamp' in df.columns:
                print(f"timestamp 数据类型: {df['timestamp'].dtype}")
                print(f"timestamp 样例:")
                print(df['timestamp'].head(3))
                print(f"是否有时区信息: {hasattr(df['timestamp'].dtype, 'tz') and df['timestamp'].dtype.tz is not None}")
                
                # 检查是否有混合的时区数据
                unique_tz = df['timestamp'].apply(lambda x: str(x.tz) if hasattr(x, 'tz') else 'no_tz').unique()
                print(f"时区信息: {unique_tz}")
            
        except Exception as e:
            print(f"错误检查 {symbol}: {e}")

if __name__ == "__main__":
    diagnose_timestamp_format()
