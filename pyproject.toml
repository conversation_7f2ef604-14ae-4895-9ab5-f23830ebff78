[project]
name = "quant-data"
version = "0.1.0"
description = "量化交易系统 - 专业级机器学习建模平台"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # 核心数据处理
    "pandas>=2.0.0,<3.0.0",
    "numpy>=1.24.0,<2.0.0",
    "pyarrow>=12.0.0,<18.0.0",

    # 机器学习
    "scikit-learn>=1.3.0,<2.0.0",
    "xgboost>=1.7.0,<3.0.0",
    "optuna>=3.0.0,<5.0.0",

    # 数据获取
    "yfinance>=0.2.40,<1.0.0",

    # 可视化
    "matplotlib>=3.7.0,<4.0.0",
    "seaborn>=0.12.0,<1.0.0",

    # 调度和自动化
    "schedule>=1.2.0,<2.0.0",

    # 其他工具
    "lxml>=4.9.0,<6.0.0",
    "joblib>=1.3.0,<2.0.0",
    "scipy>=1.10.0,<2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

# Windows特定配置
windows = [
    "colorama>=0.4.6",  # Windows终端颜色支持
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88
