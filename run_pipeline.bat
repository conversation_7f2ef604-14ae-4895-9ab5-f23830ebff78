@echo off
chcp 65001 >nul
REM Quantitative Trading System Full Pipeline Script (Windows Batch Version)
REM Function: One-click execution from data acquisition to trading signal generation
REM Usage: run_pipeline.bat [quick] - Add quick parameter to enable fast mode
REM        run_pipeline.bat      - Standard complete process

setlocal enabledelayedexpansion

REM Check if it's quick mode
set QUICK_MODE=false
if "%1"=="quick" (
    set QUICK_MODE=true
    echo ===================================================================
    echo Quantitative Trading System Fast Test Mode (Windows Version)
    echo ===================================================================
) else (
    echo ===================================================================
    echo Quantitative Trading System Complete Pipeline (Windows Version)
    echo ===================================================================
)

REM Check if uv is installed
where uv >nul 2>nul
if errorlevel 1 (
    echo Error: uv not installed, please install uv first
    echo Installation: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    pause
    exit /b 1
)

REM Check project files
if not exist "pyproject.toml" (
    echo Error: pyproject.toml not found, please run from project root directory
    pause
    exit /b 1
)

echo Dependency check completed

REM Create necessary directories
if not exist "logs" mkdir logs
if not exist "logs\feature_results" mkdir logs\feature_results
if not exist "logs\model_results" mkdir logs\model_results
if not exist "logs\trading_signals" mkdir logs\trading_signals
if not exist "logs\automation" mkdir logs\automation
if not exist "model" mkdir model
if not exist "model\data" mkdir model\data
if not exist "model\optimized_params" mkdir model\optimized_params
if not exist "model\saved_models" mkdir model\saved_models

echo Directory creation completed

REM Record start time
set start_time=%time%

REM Set Windows compatible environment variables
set PYTHONIOENCODING=utf-8
set PYTHONUNBUFFERED=1

echo.
echo ===================================================================
echo Step 1/5: Data Acquisition and Feature Calculation
echo ===================================================================
uv run python 01_fetch_and_save_data.py --auto
if errorlevel 1 (
    echo 01 Data acquisition failed, terminating process
    pause
    exit /b 1
)
echo 01 Data acquisition completed

echo.
echo ===================================================================
echo Step 2/5: Feature Management and Data Preparation
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo Fast mode: Using high quality mode ^(250 stocks^) for data consistency
    uv run python 02_use_feature_store.py --auto-quality
    if errorlevel 1 (
        echo 02 Feature management failed, trying standard mode
        uv run python 02_use_feature_store.py --auto
        if errorlevel 1 (
            echo 02 Feature management failed, terminating test
            pause
            exit /b 1
        ) else (
            echo 02 Feature management completed ^(standard mode^)
        )
    ) else (
        echo 02 Feature management completed ^(fast mode with 250 stocks^)
    )
) else (
    echo Using high quality mode ^(250 quality stocks^) for better model performance
    uv run python 02_use_feature_store.py --auto-quality
    if errorlevel 1 (
        echo 02 Feature management failed, trying standard mode
        uv run python 02_use_feature_store.py --auto
        if errorlevel 1 (
            echo 02 Feature management completely failed, but continuing execution
            set failed_02=1
        ) else (
            echo 02 Feature management completed ^(standard mode^)
        )
    ) else (
        echo 02 Feature management completed ^(high quality mode^)
    )
)

echo.
echo ===================================================================
echo Step 3/5: Model Optimization and Hyperparameter Tuning
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo Fast mode: Skipping time-consuming Walk-Forward analysis
    echo 03 Model optimization skipped ^(fast mode^)
) else (
    uv run python 03_walk_forward_analysis.py --auto
    if errorlevel 1 (
        echo 03 Model optimization failed, but continuing execution
        set failed_03=1
    ) else (
        echo 03 Model optimization completed
    )
)

echo.
echo ===================================================================
echo Step 4/5: Model Training and Backtesting
echo ===================================================================
if "%QUICK_MODE%"=="true" (
    echo Fast mode: Model training + lightweight backtesting validation
    uv run python 04_model_training.py --auto
    if errorlevel 1 (
        echo 04 Model training failed, terminating test
        pause
        exit /b 1
    ) else (
        echo 04 Model training and validation completed ^(fast mode^)
        echo Fast mode includes basic backtesting validation, check model performance
    )
) else (
    uv run python 04_model_training.py --auto
    if errorlevel 1 (
        echo 04 Model training failed, but continuing execution
        set failed_04=1
    ) else (
        echo 04 Model training completed
    )
)

echo.
echo ===================================================================
echo Step 5/5: Trading Signal Generation
echo ===================================================================
uv run python 05_trading_signal_system.py --auto
if errorlevel 1 (
    echo 05 Trading signal failed
    set failed_05=1
) else (
    echo 05 Trading signal completed
)

REM Record end time
set end_time=%time%

echo.
echo ===================================================================
echo Pipeline execution completed!
echo ===================================================================

REM Check result files
echo Result file check:
if exist "model\data\training_data.parquet" (
    echo Training data generated
) else (
    echo Training data not found
)

if exist "model\data\test_data.parquet" (
    echo Test data generated
) else (
    echo Test data not found
)

if exist "model\optimized_params\optimized_params.json" (
    echo Optimized parameters generated
) else (
    echo Optimized parameters not found
)

if exist "model\saved_models\xgboost_model.pkl" (
    echo Model file generated
) else (
    echo Model file not found
)

echo.
echo Execution Statistics:
echo   Start time: %start_time%
echo   End time: %end_time%
echo   Operating System: Windows

echo.
echo Next steps:
if "%QUICK_MODE%"=="true" (
    echo   Fast test completed! For full functionality run: run_pipeline.bat
    echo   1. Check training results: dir model\data
    echo   2. Check trading signals: dir logs\trading_signals
    echo   3. Full pipeline test: run_pipeline.bat
) else (
    echo   1. View Dashboard: uv run python tools\dashboard.py
    echo   2. Check trading signals: dir logs\trading_signals
    echo   3. Analyze model results: dir logs\model_results
    echo   4. Quick test: run_pipeline.bat quick
)
echo ===================================================================

echo Pipeline execution completed, auto exit...

REM Auto exit only if no major failures occurred
if defined failed_02 (
    echo Some steps failed, pausing for review...
    pause
) else if defined failed_03 (
    echo Some steps failed, pausing for review...
    pause
) else if defined failed_04 (
    echo Some steps failed, pausing for review...
    pause
) else if defined failed_05 (
    echo Some steps failed, pausing for review...
    pause
) else (
    echo All steps completed successfully, auto exiting in 3 seconds...
    timeout /t 3 /nobreak >nul
)
