#!/usr/bin/env python3
import json
from pathlib import Path

# Check if optimized parameters will pass validation
params_file = Path('model/optimized_params/optimized_params.json')
if params_file.exists():
    with open(params_file, 'r') as f:
        data = json.load(f)
    
    best_params = data['walk_forward_analysis']['best_params']
    best_auc = data['walk_forward_analysis']['best_window_auc']
    
    print(f'检查优化参数: {best_params}')
    print(f'最佳AUC: {best_auc}')
    
    # Simulate validation logic
    max_depth = best_params.get('max_depth', 6)
    learning_rate = best_params.get('learning_rate', 0.1)
    n_estimators = best_params.get('n_estimators', 100)
    
    print(f'参数值: max_depth={max_depth}, lr={learning_rate:.4f}, n_estimators={n_estimators}')
    
    # New validation criteria
    valid = True
    if max_depth < 3: 
        print('❌ max_depth太小')
        valid = False
    if max_depth > 10: 
        print('❌ max_depth太大')  
        valid = False
    if learning_rate < 0.01: 
        print('❌ learning_rate太小')
        valid = False
    if learning_rate > 0.5: 
        print('❌ learning_rate太大')
        valid = False
    if n_estimators > 500: 
        print('❌ n_estimators太多')
        valid = False
    if best_auc > 0.85: 
        print('❌ AUC过高')
        valid = False
    
    print(f'✅ 参数验证结果: {"通过" if valid else "失败"}')
