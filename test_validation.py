import json
from pathlib import Path

# 检查哪些参数会被使用
params_file = Path('model/optimized_params/optimized_params.json')
if params_file.exists():
    with open(params_file, 'r') as f:
        data = json.load(f)
    
    best_params = data['walk_forward_analysis']['best_params']
    best_auc = data['walk_forward_analysis']['best_window_auc']
    
    # 严格验证（与高性能版本相同）
    max_depth = best_params.get('max_depth', 6)
    learning_rate = best_params.get('learning_rate', 0.1)
    
    will_pass = True
    if max_depth < 5: will_pass = False  # 这应该失败
    if learning_rate < 0.08: will_pass = False  # 这应该失败
    
    print(f'优化参数: max_depth={max_depth}, lr={learning_rate:.4f}')
    print(f'严格验证结果: {"通过" if will_pass else "失败 - 将使用默认参数"}')
    print(f'预期使用参数: {"优化参数" if will_pass else "默认参数(n_estimators=100, max_depth=6, lr=0.1)"}')
