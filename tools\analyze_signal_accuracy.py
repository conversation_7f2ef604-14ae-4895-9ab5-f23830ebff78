#!/usr/bin/env python3
"""
深度分析买入信号准确率
分析34.3%准确率的含义、合理性和优化空间
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from datetime import datetime
import importlib.util

# Import from the trading signal system
spec = importlib.util.spec_from_file_location("trading_system", "05_trading_signal_system.py")
trading_system = importlib.util.module_from_spec(spec)
spec.loader.exec_module(trading_system)

ThresholdOptimizer = trading_system.ThresholdOptimizer

def analyze_accuracy_meaning():
    """分析准确率的含义和计算逻辑"""
    print("=== 买入准确率深度分析 ===\n")
    
    # 加载数据
    test_data = pd.read_parquet("model/data/test_data.parquet")
    
    print("1. 数据基本信息:")
    print(f"  测试样本总数: {len(test_data):,}")
    print(f"  正例比例(target=1): {test_data['target'].mean():.1%}")
    print(f"  负例比例(target=0): {(1-test_data['target'].mean()):.1%}")
    
    # 分析target的含义
    print(f"\n2. Target标签含义:")
    print(f"  target=1: 未来10天涨幅 > 5%")
    print(f"  target=0: 未来10天涨幅 ≤ 5%")
    print(f"  历史统计: {test_data['target'].sum():,}个样本在10天内涨超5%")
    
    return test_data

def analyze_threshold_impact():
    """分析不同阈值对准确率的影响"""
    print("\n3. 阈值vs准确率分析:")
    
    # 创建优化器
    optimizer = ThresholdOptimizer()
    
    # 加载测试数据
    test_data = pd.read_parquet("model/data/test_data.parquet")
    
    # 准备特征
    feature_columns = [
        'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
        'macd', 'macd_signal', 'macd_histogram', 'rsi',
        'sma_20', 'sma_50', 'ema_12', 'ema_26',
        'bb_upper', 'bb_middle', 'bb_lower',
        'atr', 'volume_sma', 'price_momentum', 'volatility'
    ]
    
    available_features = [col for col in feature_columns if col in test_data.columns]
    X = test_data[available_features].fillna(0)
    
    # 预测概率
    probabilities = optimizer.predictor.predict_probability(X)
    y_true = test_data['target'].values
    
    # 分析不同阈值的效果
    thresholds = [0.15, 0.20, 0.25, 0.30, 0.35, 0.40, 0.50, 0.60]
    
    print("  买入阈值 | 信号数量 | 信号比例 | 买入准确率 | 预期收益")
    print("  --------|---------|---------|----------|--------")
    
    results = []
    for threshold in thresholds:
        buy_mask = probabilities >= threshold
        buy_signals = buy_mask.sum()
        buy_ratio = buy_signals / len(probabilities)
        buy_accuracy = np.mean(y_true[buy_mask]) if buy_signals > 0 else 0
        
        # 计算预期收益 (考虑5%涨幅目标)
        expected_return = buy_accuracy * 0.05 + (1 - buy_accuracy) * (-0.02)  # 假设错误时平均亏损2%
        
        print(f"  {threshold:>7.2f} | {buy_signals:>7,} | {buy_ratio:>7.1%} | {buy_accuracy:>8.1%} | {expected_return:>7.1%}")
        
        results.append({
            'threshold': threshold,
            'signals': buy_signals,
            'ratio': buy_ratio,
            'accuracy': buy_accuracy,
            'expected_return': expected_return
        })
    
    return results, probabilities, y_true

def analyze_baseline_comparison():
    """与基准策略对比"""
    print("\n4. 基准对比分析:")
    
    # 随机策略
    test_data = pd.read_parquet("model/data/test_data.parquet")
    random_accuracy = test_data['target'].mean()
    
    print(f"  随机买入准确率: {random_accuracy:.1%}")
    print(f"  我们的买入准确率: 34.3%")
    print(f"  相对提升: {(0.343/random_accuracy - 1)*100:.1f}%")
    
    # 计算信息比率 (Information Ratio)
    excess_return = 0.343 - random_accuracy
    print(f"  超额准确率: {excess_return:.1%}")
    
    return random_accuracy

def analyze_signal_quality_distribution():
    """分析信号质量分布"""
    print("\n5. 信号质量分布分析:")
    
    # 创建优化器
    optimizer = ThresholdOptimizer()
    
    # 加载测试数据
    test_data = pd.read_parquet("model/data/test_data.parquet")
    
    # 准备特征
    feature_columns = [
        'ohlc_avg', 'hl_avg', 'price_change', 'volume_change',
        'macd', 'macd_signal', 'macd_histogram', 'rsi',
        'sma_20', 'sma_50', 'ema_12', 'ema_26',
        'bb_upper', 'bb_middle', 'bb_lower',
        'atr', 'volume_sma', 'price_momentum', 'volatility'
    ]
    
    available_features = [col for col in feature_columns if col in test_data.columns]
    X = test_data[available_features].fillna(0)
    
    # 预测概率
    probabilities = optimizer.predictor.predict_probability(X)
    y_true = test_data['target'].values
    
    # 按概率区间分析准确率
    prob_ranges = [(0.0, 0.2), (0.2, 0.3), (0.3, 0.4), (0.4, 0.5), (0.5, 0.6), (0.6, 1.0)]
    
    print("  概率区间 | 样本数量 | 实际准确率 | 理论准确率")
    print("  --------|---------|----------|----------")
    
    for low, high in prob_ranges:
        mask = (probabilities >= low) & (probabilities < high)
        if mask.sum() > 0:
            actual_acc = np.mean(y_true[mask])
            theoretical_acc = np.mean(probabilities[mask])
            sample_count = mask.sum()
            
            print(f"  {low:.1f}-{high:.1f}  | {sample_count:>7,} | {actual_acc:>8.1%} | {theoretical_acc:>9.1%}")

def calculate_profitability_metrics():
    """计算盈利能力指标"""
    print("\n6. 盈利能力分析:")
    
    # 当前参数下的收益分析
    buy_accuracy = 0.343
    target_return = 0.05  # 5%目标收益
    avg_loss = -0.02     # 假设平均亏损2%
    
    # 期望收益计算
    expected_return = buy_accuracy * target_return + (1 - buy_accuracy) * avg_loss
    
    print(f"  目标收益: {target_return:.1%}")
    print(f"  买入准确率: {buy_accuracy:.1%}")
    print(f"  假设平均亏损: {avg_loss:.1%}")
    print(f"  期望收益: {expected_return:.2%}")
    
    # 盈亏比分析
    win_rate = buy_accuracy
    profit_per_win = target_return
    loss_per_loss = abs(avg_loss)
    
    profit_loss_ratio = profit_per_win / loss_per_loss
    print(f"  盈亏比: {profit_loss_ratio:.2f}:1")
    
    # Kelly公式建议仓位
    kelly_fraction = (win_rate * profit_per_win - (1 - win_rate) * loss_per_loss) / profit_per_win
    print(f"  Kelly公式建议仓位: {max(0, kelly_fraction):.1%}")
    
    return expected_return, kelly_fraction

def recommend_optimization_strategy():
    """推荐优化策略"""
    print("\n7. 优化建议:")
    
    print("📊 当前34.3%买入准确率评估:")
    print("  ✅ 优势:")
    print("    - 显著超越随机策略(20.4%)")
    print("    - 相对提升68%")
    print("    - 解决了之前无买入信号的问题")
    
    print("  ⚠️ 挑战:")
    print("    - 绝对准确率偏低")
    print("    - 期望收益可能为负")
    print("    - 需要严格的风险管理")
    
    print("\n💡 优化策略建议:")
    print("  1. 提高阈值策略:")
    print("     - 将买入阈值从0.25提升到0.35-0.40")
    print("     - 牺牲信号数量换取质量提升")
    print("     - 目标准确率45-50%")
    
    print("  2. 多层筛选策略:")
    print("     - 概率阈值 + 技术指标确认")
    print("     - 加入趋势、动量等二次筛选")
    print("     - 组合多个信号源")
    
    print("  3. 动态阈值策略:")
    print("     - 根据市场波动率调整阈值")
    print("     - 牛市降低阈值，熊市提高阈值")
    print("     - 考虑行业轮动和季节性")
    
    print("  4. 风险管理优化:")
    print("     - 严格止损机制(2-3%)")
    print("     - 分批建仓和止盈")
    print("     - 组合多个时间周期信号")

def save_analysis_results(threshold_results, baseline_accuracy, expected_return):
    """保存分析结果"""
    results = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'analysis_summary': {
            'current_buy_accuracy': 0.343,
            'baseline_random_accuracy': float(baseline_accuracy),
            'relative_improvement': float((0.343/baseline_accuracy - 1)*100),
            'expected_return': float(expected_return),
            'recommendation': 'OPTIMIZE_THRESHOLDS'
        },
        'threshold_analysis': threshold_results,
        'optimization_suggestions': [
            'Increase buy threshold to 0.35-0.40 for higher accuracy',
            'Implement multi-layer filtering with technical indicators',
            'Add dynamic threshold adjustment based on market conditions',
            'Enhance risk management with strict stop-loss'
        ]
    }
    
    results_dir = Path("logs/signal_analysis")
    results_dir.mkdir(exist_ok=True)
    
    results_file = results_dir / f"accuracy_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📁 分析结果已保存: {results_file}")

def main():
    """主分析函数"""
    print("=" * 60)
    print("🔍 买入信号准确率深度分析")
    print("=" * 60)
    
    # 1. 分析准确率含义
    test_data = analyze_accuracy_meaning()
    
    # 2. 阈值影响分析
    threshold_results, probabilities, y_true = analyze_threshold_impact()
    
    # 3. 基准对比
    baseline_accuracy = analyze_baseline_comparison()
    
    # 4. 信号质量分布
    analyze_signal_quality_distribution()
    
    # 5. 盈利能力分析
    expected_return, kelly_fraction = calculate_profitability_metrics()
    
    # 6. 优化建议
    recommend_optimization_strategy()
    
    # 7. 保存结果
    save_analysis_results(threshold_results, baseline_accuracy, expected_return)
    
    print("\n" + "=" * 60)
    print("🎯 结论: 34.3%准确率需要优化")
    print("📈 建议: 提高买入阈值到0.35-0.40，目标准确率45-50%")
    print("⚠️ 风险: 当前期望收益可能为负，需要严格风险管理")
    print("=" * 60)

if __name__ == "__main__":
    main()