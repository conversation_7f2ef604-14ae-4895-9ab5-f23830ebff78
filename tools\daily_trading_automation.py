#!/usr/bin/env python3
"""
每日交易自动化系统
定时运行数据更新、模型预测和交易信号生成
"""

import subprocess
import sys
from datetime import datetime, time
import schedule
import time as time_module
from pathlib import Path
import json
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/automation/trading_automation.log'),
        logging.StreamHandler()
    ]
)

class TradingAutomation:
    """交易自动化系统"""
    
    def __init__(self):
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录"""
        Path("logs").mkdir(exist_ok=True)
        Path("logs/trading_signals").mkdir(exist_ok=True)
        Path("logs/automation").mkdir(exist_ok=True)
        Path("logs/feature_results").mkdir(exist_ok=True)
        
    def run_script(self, script_name: str, description: str) -> bool:
        """运行Python脚本"""
        logging.info(f"开始执行: {description}")
        
        try:
            # 分割脚本名和参数
            cmd_parts = script_name.split()
            cmd = [sys.executable] + cmd_parts
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                logging.info(f"✅ {description} 执行成功")
                return True
            else:
                logging.error(f"❌ {description} 执行失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logging.error(f"❌ {description} 执行超时")
            return False
        except Exception as e:
            logging.error(f"❌ {description} 执行出错: {e}")
            return False
    
    def daily_update_routine(self):
        """每日更新例程"""
        logging.info("=" * 60)
        logging.info("开始每日交易数据更新和分析")
        logging.info("=" * 60)
        
        start_time = datetime.now()
        
        # 步骤1: 数据更新
        if not self.run_script("01_fetch_and_save_data.py", "数据获取和特征计算"):
            logging.error("数据更新失败，跳过后续步骤")
            return
        
        # 步骤2: 特征管理
        if not self.run_script("02_use_feature_store.py --auto", "特征管理和数据准备"):
            logging.error("特征管理失败，跳过后续步骤")
            return
        
        # 步骤3: 模型训练（每周一次）
        if datetime.now().weekday() == 0:  # 周一
            if not self.run_script("03_model_training.py --auto", "模型训练和回测"):
                logging.warning("模型训练失败，使用现有模型")
        
        # 步骤4: 交易信号生成
        if not self.run_script("05_trading_signal_system.py", "交易信号生成"):
            logging.error("交易信号生成失败")
            return
        
        # 步骤5: 深度分析（可选）
        if datetime.now().weekday() == 0:  # 周一进行深度分析
            self.run_script("04_feature_model_analysis.py", "深度特征分析")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logging.info(f"每日例程完成，耗时: {duration}")
        
        # 生成简要报告
        self.generate_automation_report(start_time, end_time, duration)
    
    def generate_automation_report(self, start_time: datetime, end_time: datetime, duration):
        """生成自动化报告"""
        
        # 读取最新的交易信号
        signals_dir = Path("logs/trading_signals")
        if signals_dir.exists():
            latest_signal_files = sorted(signals_dir.glob("daily_report_*.json"))
            if latest_signal_files:
                with open(latest_signal_files[-1], 'r', encoding='utf-8') as f:
                    latest_report = json.load(f)
                
                report = {
                    'automation_summary': {
                        'date': start_time.strftime('%Y-%m-%d'),
                        'start_time': start_time.strftime('%H:%M:%S'),
                        'end_time': end_time.strftime('%H:%M:%S'),
                        'duration_minutes': duration.total_seconds() / 60,
                        'status': 'success'
                    },
                    'trading_summary': latest_report.get('market_analysis', {}),
                    'recommended_actions': latest_report.get('recommended_actions', {}),
                    'top_opportunities': latest_report.get('top_opportunities', [])[:3]
                }
                
                # 保存自动化报告
                automation_report_file = f"logs/automation/automation_report_{start_time.strftime('%Y%m%d')}.json"
                with open(automation_report_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                
                logging.info(f"自动化报告已保存: {automation_report_file}")
                
                # 打印关键信息
                if 'market_analysis' in latest_report:
                    analysis = latest_report['market_analysis']
                    logging.info(f"今日信号: {analysis.get('buy_signals', 0)}买入, {analysis.get('sell_signals', 0)}卖出")
                
                if 'recommended_actions' in latest_report:
                    actions = latest_report['recommended_actions']
                    buy_count = len(actions.get('buy_recommendations', []))
                    total_investment = actions.get('total_investment', 0)
                    logging.info(f"推荐操作: {buy_count}个买入机会, 总投资${total_investment:,.2f}")

def setup_schedule():
    """设置定时任务"""
    automation = TradingAutomation()
    
    # 每个交易日运行
    schedule.every().monday.at("17:30").do(automation.daily_update_routine)
    schedule.every().tuesday.at("17:30").do(automation.daily_update_routine)
    schedule.every().wednesday.at("17:30").do(automation.daily_update_routine)
    schedule.every().thursday.at("17:30").do(automation.daily_update_routine)
    schedule.every().friday.at("17:30").do(automation.daily_update_routine)
    
    logging.info("定时任务已设置: 每个交易日 17:30 执行")
    logging.info("任务内容: 数据更新 → 特征管理 → 信号生成")

def run_now():
    """立即运行一次"""
    automation = TradingAutomation()
    automation.daily_update_routine()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='交易自动化系统')
    parser.add_argument('--now', action='store_true', help='立即运行一次')
    parser.add_argument('--schedule', action='store_true', help='启动定时任务')
    
    args = parser.parse_args()
    
    if args.now:
        print("🚀 立即运行交易自动化流程...")
        run_now()
    elif args.schedule:
        print("⏰ 启动定时任务...")
        setup_schedule()
        
        try:
            while True:
                schedule.run_pending()
                time_module.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            print("定时任务已停止")
    else:
        print("使用方法:")
        print("  python daily_trading_automation.py --now      # 立即运行")
        print("  python daily_trading_automation.py --schedule # 启动定时任务")

if __name__ == "__main__":
    main()